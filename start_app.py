#!/usr/bin/env python3
"""
Startup script for the Enhanced RAG Application
"""

import os
import sys

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = {
        'flask': 'flask',
        'firebase-admin': 'firebase_admin',
        'llama-cpp-python': 'llama_cpp'
    }

    missing_packages = []

    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)

    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n📦 Install missing packages with:")
        print("   pip install -r requirements.txt")
        return False

    print("✅ All required packages are installed")
    return True

def check_files():
    """Check if required files exist"""
    required_files = [
        'serviceKey.json',
        'Meta-Llama-3-8B-Instruct.Q4_K_S.gguf'
    ]

    missing_files = []

    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)

    if missing_files:
        print("❌ Missing required files:")
        for file in missing_files:
            print(f"   - {file}")
        print("\n📁 Please ensure these files are in the project directory:")
        print("   - serviceKey.json: Firebase service account key")
        print("   - Meta-Llama-3-8B-Instruct.Q4_K_S.gguf: LLM model file")
        return False

    print("✅ All required files are present")
    return True

def set_environment():
    """Set default environment variables if not already set"""
    env_vars = {
        'SERVICE_ACCOUNT_FILE': './serviceKey.json',
        'LLAMA_MODEL_PATH': './Meta-Llama-3-8B-Instruct.Q4_K_S.gguf',
        'DEFAULT_LANGUAGE': 'en',
        'LOG_LEVEL': 'INFO',
        'FLASK_HOST': '0.0.0.0',
        'FLASK_PORT': '5000'
    }

    for key, value in env_vars.items():
        if key not in os.environ:
            os.environ[key] = value

    print("✅ Environment variables configured")

def start_application():
    """Start the RAG application"""
    print("\n🚀 Starting Enhanced RAG Application...")
    print("   - Multi-language support: English, Turkish, Azerbaijani")
    print("   - Enhanced retrieval with TF-IDF")
    print("   - Intelligent caching")
    print("   - Performance optimizations")
    print("\n" + "="*50)

    try:
        # Import and run the application
        from rag_firestore_app import app, config

        print(f"🌐 Server starting on http://{config.FLASK_HOST}:{config.FLASK_PORT}")
        print("📊 Available endpoints:")
        print("   - POST /chat - Chat with the assistant")
        print("   - POST /reload_data - Reload data from Firestore")
        print("   - GET /status - System status")
        print("   - GET /health - Health check")
        print("\n💡 Test the application with: python test_app.py")
        print("🛑 Press Ctrl+C to stop the server")
        print("="*50)

        app.run(
            host=config.FLASK_HOST,
            port=config.FLASK_PORT,
            debug=config.FLASK_DEBUG,
            threaded=True
        )

    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting application: {e}")
        sys.exit(1)

def main():
    """Main startup function"""
    print("🔧 Enhanced RAG Application Startup")
    print("="*40)

    # Check dependencies
    if not check_dependencies():
        sys.exit(1)

    # Check required files
    if not check_files():
        sys.exit(1)

    # Set environment variables
    set_environment()

    # Start the application
    start_application()

if __name__ == "__main__":
    main()
