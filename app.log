2025-05-24 23:34:17,912 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-24 23:34:17,914 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-24 23:34:35,162 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 23:34:35] "GET /health HTTP/1.1" 200 -
2025-05-24 23:34:37,192 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 23:34:37] "GET /status HTTP/1.1" 200 -
2025-05-24 23:34:39,221 - rag_firestore_app - INFO - Request received to reload data from Firestore.
2025-05-24 23:34:39,222 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 23:34:39] "[35m[1mPOST /reload_data HTTP/1.1[0m" 500 -
2025-05-24 23:34:41,269 - rag_firestore_app - ERROR - Exception on /chat [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\backend\rag_firestore_app.py", line 223, in chat_handler
    logger.info(i18n.get_text('new_query_received', 'en', user_query))
                ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: I18nManager.get_text() takes from 2 to 3 positional arguments but 4 were given

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\backend\rag_firestore_app.py", line 235, in chat_handler
    error_msg = i18n.get_text('chat_request_error', 'en', str(e))
TypeError: I18nManager.get_text() takes from 2 to 3 positional arguments but 4 were given
2025-05-24 23:34:41,274 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 23:34:41] "[35m[1mPOST /chat HTTP/1.1[0m" 500 -
2025-05-24 23:34:44,304 - rag_firestore_app - ERROR - Exception on /chat [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\backend\rag_firestore_app.py", line 223, in chat_handler
    logger.info(i18n.get_text('new_query_received', 'en', user_query))
                ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: I18nManager.get_text() takes from 2 to 3 positional arguments but 4 were given

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\backend\rag_firestore_app.py", line 235, in chat_handler
    error_msg = i18n.get_text('chat_request_error', 'en', str(e))
TypeError: I18nManager.get_text() takes from 2 to 3 positional arguments but 4 were given
2025-05-24 23:34:44,306 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 23:34:44] "[35m[1mPOST /chat HTTP/1.1[0m" 500 -
2025-05-24 23:34:47,338 - rag_firestore_app - ERROR - Exception on /chat [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\backend\rag_firestore_app.py", line 223, in chat_handler
    logger.info(i18n.get_text('new_query_received', 'en', user_query))
                ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: I18nManager.get_text() takes from 2 to 3 positional arguments but 4 were given

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\backend\rag_firestore_app.py", line 235, in chat_handler
    error_msg = i18n.get_text('chat_request_error', 'en', str(e))
TypeError: I18nManager.get_text() takes from 2 to 3 positional arguments but 4 were given
2025-05-24 23:34:47,339 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 23:34:47] "[35m[1mPOST /chat HTTP/1.1[0m" 500 -
2025-05-24 23:34:50,365 - rag_firestore_app - ERROR - Exception on /chat [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\backend\rag_firestore_app.py", line 223, in chat_handler
    logger.info(i18n.get_text('new_query_received', 'en', user_query))
                ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: I18nManager.get_text() takes from 2 to 3 positional arguments but 4 were given

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\backend\rag_firestore_app.py", line 235, in chat_handler
    error_msg = i18n.get_text('chat_request_error', 'en', str(e))
TypeError: I18nManager.get_text() takes from 2 to 3 positional arguments but 4 were given
2025-05-24 23:34:50,368 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 23:34:50] "[35m[1mPOST /chat HTTP/1.1[0m" 500 -
2025-05-24 23:34:53,383 - rag_firestore_app - ERROR - Exception on /chat [POST]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\backend\rag_firestore_app.py", line 223, in chat_handler
    logger.info(i18n.get_text('new_query_received', 'en', user_query))
                ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: I18nManager.get_text() takes from 2 to 3 positional arguments but 4 were given

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\backend\rag_firestore_app.py", line 235, in chat_handler
    error_msg = i18n.get_text('chat_request_error', 'en', str(e))
TypeError: I18nManager.get_text() takes from 2 to 3 positional arguments but 4 were given
2025-05-24 23:34:53,385 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 23:34:53] "[35m[1mPOST /chat HTTP/1.1[0m" 500 -
2025-05-24 23:40:20,333 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-24 23:40:20,333 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-24 23:41:04,665 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 23:41:04] "GET /health HTTP/1.1" 200 -
2025-05-24 23:41:06,686 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 23:41:06] "GET /status HTTP/1.1" 200 -
2025-05-24 23:41:08,742 - rag_firestore_app - INFO - Request received to reload data from Firestore.
2025-05-24 23:41:08,743 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 23:41:08] "[35m[1mPOST /reload_data HTTP/1.1[0m" 500 -
2025-05-24 23:41:10,776 - rag_firestore_app - INFO - New query received: What restaurants do you have?
2025-05-24 23:41:10,777 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 23:41:10] "POST /chat HTTP/1.1" 200 -
2025-05-24 23:41:13,806 - rag_firestore_app - INFO - New query received: Hangi restoranlar var?
2025-05-24 23:41:13,807 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 23:41:13] "POST /chat HTTP/1.1" 200 -
2025-05-24 23:41:16,934 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 23:41:16] "POST /chat HTTP/1.1" 200 -
2025-05-24 23:41:19,975 - rag_firestore_app - INFO - New query received: Tell me about the menu
2025-05-24 23:41:19,976 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 23:41:19] "POST /chat HTTP/1.1" 200 -
2025-05-24 23:41:23,007 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 23:41:23] "POST /chat HTTP/1.1" 200 -
