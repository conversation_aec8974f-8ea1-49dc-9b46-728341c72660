from typing import Dict, Any

class I18nManager:
    """Internationalization manager for multi-language support"""
    
    def __init__(self):
        self.translations = {
            'en': {
                # System messages
                'firebase_init_success': 'Firebase Admin SDK successfully initialized and Firestore client created.',
                'firebase_init_error': 'Error occurred while initializing Firebase: {}',
                'firebase_file_not_found': 'Firebase service account file not found: {}',
                'firebase_file_missing': "Service account file '{}' not found.",
                
                # Llama model messages
                'llama_model_loaded': "Llama model '{}' successfully loaded.",
                'llama_model_error': 'Error occurred while loading Llama model: {}',
                'llama_model_not_found': 'Llama model file not found: {}',
                'llama_model_missing': "Llama model file '{}' not found.",
                
                # Data loading messages
                'firestore_loading': 'Loading data from Firestore...',
                'firestore_loaded': 'Successfully loaded and processed {} documents from Firestore.',
                'firestore_load_error': 'Error loading data from Firestore: {}',
                'firestore_client_not_initialized': 'Firestore client not initialized. Cannot load data.',
                'firestore_cache_empty': 'Firestore data cache is empty. Reloading data.',
                'firestore_reload_request': 'Request received to reload data from Firestore.',
                'firestore_reload_success': 'Data successfully reloaded from Firestore. {} documents processed.',
                'firestore_reload_error': 'Error occurred while reloading data.',
                
                # Retrieval messages
                'retriever_found_docs': "Retriever found {} potential documents for query '{}', selected top {} documents.",
                'no_relevant_docs': "No relevant documents found for query '{}'. Proceeding without context or providing default response.",
                
                # LLM messages
                'llm_response_generated': 'Response generated from LLM: {}',
                'llm_response_error': 'Error generating response from LLM: {}',
                'llm_not_loaded': 'Error: Llama model not loaded.',
                'llm_generation_error': 'Problem encountered while generating response from model.',
                
                # API messages
                'new_query_received': 'New query received: {}',
                'query_field_missing': 'Query field is missing.',
                'chat_request_error': 'Error processing chat request: {}',
                'server_error': 'Server error occurred while processing request.',
                
                # Application messages
                'app_starting': 'Application starting...',
                'app_startup_data_load_failed': 'Could not load Firestore data at startup. Try using the /reload_data endpoint.',
                'flask_app_starting': 'Flask application starting on 0.0.0.0:5000...',
                'required_components_failed': 'Application cannot run because required components (Firebase or Llama model) could not be initialized.',
                
                # Data field labels
                'restaurant_name': 'Restaurant Name',
                'description': 'Description',
                'cuisine_types': 'Cuisine Types',
                'address': 'Address',
                'services': 'Services',
                'features': 'Features',
                'atmosphere': 'Atmosphere',
                'menu_item': 'Menu Item',
                'category': 'Category',
                'price': 'Price',
                'ingredients': 'Ingredients',
                'allergens': 'Allergens',
                'unknown_restaurant': 'Unknown Restaurant',
                'unknown_product': 'Unknown Product',
                'not_specified': 'Not specified',
                'none': 'None',
                
                # Error responses
                'no_answer_found': 'I could not find an answer to this topic in the provided information.',
                'no_data_available': 'Error: No data available.',
                'data_load_failed': 'Error: Could not load data from Firestore.',
                'sorry_no_info': 'Sorry, I could not find specific information related to your question in my database.',
                
                # Prompt templates
                'system_prompt': """You are a helpful assistant that answers questions about restaurants, menus, and food.
Only answer using the texts provided in the 'Provided Information' section below.
If the answer is not in this information, say "I could not find an answer to this topic in the provided information."
Keep your answers short and concise. Do not guess. Do not go beyond the provided information.
Use the correct names of restaurants, menu items, etc. from the information given to you.
Do not mention which information you used at the end of your answer (e.g., "According to Information 1..."). Give the answer directly.""",
                'provided_info': 'Provided Information',
                'question': 'Question'
            },
            'tr': {
                # System messages
                'firebase_init_success': 'Firebase Admin SDK başarıyla başlatıldı ve Firestore istemcisi oluşturuldu.',
                'firebase_init_error': 'Firebase başlatılırken hata oluştu: {}',
                'firebase_file_not_found': 'Firebase service account dosyası bulunamadı: {}',
                'firebase_file_missing': "Service account dosyası '{}' bulunamadı.",
                
                # Llama model messages
                'llama_model_loaded': "Llama modeli '{}' başarıyla yüklendi.",
                'llama_model_error': 'Llama modeli yüklenirken hata oluştu: {}',
                'llama_model_not_found': 'Llama model dosyası bulunamadı: {}',
                'llama_model_missing': "Llama model dosyası '{}' bulunamadı.",
                
                # Data loading messages
                'firestore_loading': 'Firestore\'dan veri yükleniyor...',
                'firestore_loaded': 'Firestore\'dan {} adet doküman başarıyla yüklendi ve işlendi.',
                'firestore_load_error': 'Firestore\'dan veri yüklenirken hata: {}',
                'firestore_client_not_initialized': 'Firestore istemcisi başlatılmamış. Veri yüklenemiyor.',
                'firestore_cache_empty': 'Firestore veri önbelleği boş. Veriler yeniden yükleniyor.',
                'firestore_reload_request': 'Firestore\'dan verileri yeniden yükleme isteği alındı.',
                'firestore_reload_success': 'Veriler Firestore\'dan başarıyla yeniden yüklendi. {} doküman işlendi.',
                'firestore_reload_error': 'Veriler yeniden yüklenirken bir hata oluştu.',
                
                # Retrieval messages
                'retriever_found_docs': "Retriever, sorgu '{}' için {} potansiyel doküman buldu, en iyi {} tanesi seçildi.",
                'no_relevant_docs': "Sorgu '{}' için ilgili doküman bulunamadı. LLM'e bağlam olmadan gidiliyor veya varsayılan cevap veriliyor.",
                
                # LLM messages
                'llm_response_generated': 'LLM\'den üretilen cevap: {}',
                'llm_response_error': 'LLM\'den cevap üretilirken hata: {}',
                'llm_not_loaded': 'Hata: Llama modeli yüklenmemiş.',
                'llm_generation_error': 'Modelden cevap üretilirken bir sorunla karşılaşıldı.',
                
                # API messages
                'new_query_received': 'Yeni sorgu alındı: {}',
                'query_field_missing': 'Sorgu (query) alanı eksik.',
                'chat_request_error': 'Chat isteği işlenirken hata: {}',
                'server_error': 'İstek işlenirken sunucu hatası oluştu.',
                
                # Application messages
                'app_starting': 'Uygulama başlatılıyor...',
                'app_startup_data_load_failed': 'Başlangıçta Firestore verileri yüklenemedi. /reload_data endpoint\'ini kullanmayı deneyin.',
                'flask_app_starting': 'Flask uygulaması 0.0.0.0:5000 adresinde başlatılıyor...',
                'required_components_failed': 'Gerekli bileşenler (Firebase veya Llama modeli) başlatılamadığı için uygulama çalıştırılamıyor.',
                
                # Data field labels
                'restaurant_name': 'Restoran Adı',
                'description': 'Açıklama',
                'cuisine_types': 'Mutfak Türleri',
                'address': 'Adres',
                'services': 'Hizmetler',
                'features': 'Özellikler',
                'atmosphere': 'Atmosfer',
                'menu_item': 'Menü Öğesi',
                'category': 'Kategori',
                'price': 'Fiyat',
                'ingredients': 'İçindekiler',
                'allergens': 'Alerjenler',
                'unknown_restaurant': 'Bilinmeyen Restoran',
                'unknown_product': 'Bilinmeyen Ürün',
                'not_specified': 'Belirtilmemiş',
                'none': 'Yok',
                
                # Error responses
                'no_answer_found': 'Bu konuda sağlanan bilgilerde bir cevap bulamadım.',
                'no_data_available': 'Hata: Kullanılabilir veri yok.',
                'data_load_failed': 'Hata: Firestore\'dan veriler yüklenemedi.',
                'sorry_no_info': 'Üzgünüm, sorunuzla ilgili spesifik bir bilgi veritabanımda bulamadım.',
                
                # Prompt templates
                'system_prompt': """Sen Türkiye'deki restoranlar, menüler ve yemekler hakkında soruları yanıtlayan bir yardımcı asistansın.
Yalnızca aşağıda 'Sağlanan Bilgiler' bölümünde verilen metinleri kullanarak cevap ver.
Eğer cevap bu bilgilerde yoksa, "Bu konuda sağlanan bilgilerde bir cevap bulamadım." de.
Cevaplarını kısa ve öz tut. Tahminde bulunma. Sağlanan bilgilerin dışına çıkma.
Sana verilen bilgilerdeki restoran adı, menü öğesi adı gibi özel isimleri doğru kullan.
Cevabının sonunda hangi bilgiyi kullandığını belirtme (örneğin, "Bilgi 1'e göre..."). Direkt cevabı ver.""",
                'provided_info': 'Sağlanan Bilgiler',
                'question': 'Soru'
            },
            'az': {
                # System messages
                'firebase_init_success': 'Firebase Admin SDK uğurla işə salındı və Firestore müştərisi yaradıldı.',
                'firebase_init_error': 'Firebase işə salınarkən xəta baş verdi: {}',
                'firebase_file_not_found': 'Firebase xidmət hesabı faylı tapılmadı: {}',
                'firebase_file_missing': "Xidmət hesabı faylı '{}' tapılmadı.",
                
                # Llama model messages
                'llama_model_loaded': "Llama modeli '{}' uğurla yükləndi.",
                'llama_model_error': 'Llama modeli yüklənərkən xəta baş verdi: {}',
                'llama_model_not_found': 'Llama model faylı tapılmadı: {}',
                'llama_model_missing': "Llama model faylı '{}' tapılmadı.",
                
                # Data loading messages
                'firestore_loading': 'Firestore-dan məlumat yüklənir...',
                'firestore_loaded': 'Firestore-dan {} sənəd uğurla yükləndi və işləndi.',
                'firestore_load_error': 'Firestore-dan məlumat yüklənərkən xəta: {}',
                'firestore_client_not_initialized': 'Firestore müştərisi işə salınmayıb. Məlumat yüklənə bilmir.',
                'firestore_cache_empty': 'Firestore məlumat keşi boşdur. Məlumatlar yenidən yüklənir.',
                'firestore_reload_request': 'Firestore-dan məlumatları yenidən yükləmək üçün sorğu alındı.',
                'firestore_reload_success': 'Məlumatlar Firestore-dan uğurla yenidən yükləndi. {} sənəd işləndi.',
                'firestore_reload_error': 'Məlumatlar yenidən yüklənərkən xəta baş verdi.',
                
                # Retrieval messages
                'retriever_found_docs': "Axtarış sistemi '{}' sorğusu üçün {} potensial sənəd tapdı, ən yaxşı {} seçildi.",
                'no_relevant_docs': "'{}' sorğusu üçün uyğun sənəd tapılmadı. Kontekstsiz davam edilir və ya standart cavab verilir.",
                
                # LLM messages
                'llm_response_generated': 'LLM-dən yaradılan cavab: {}',
                'llm_response_error': 'LLM-dən cavab yaradılarkən xəta: {}',
                'llm_not_loaded': 'Xəta: Llama modeli yüklənməyib.',
                'llm_generation_error': 'Modeldən cavab yaradılarkən problem yaşandı.',
                
                # API messages
                'new_query_received': 'Yeni sorğu alındı: {}',
                'query_field_missing': 'Sorğu sahəsi çatışmır.',
                'chat_request_error': 'Söhbət sorğusu işlənərkən xəta: {}',
                'server_error': 'Sorğu işlənərkən server xətası baş verdi.',
                
                # Application messages
                'app_starting': 'Tətbiq başladılır...',
                'app_startup_data_load_failed': 'Başlanğıcda Firestore məlumatları yüklənə bilmədi. /reload_data endpoint-ini istifadə etməyi sınayın.',
                'flask_app_starting': 'Flask tətbiqi 0.0.0.0:5000 ünvanında başladılır...',
                'required_components_failed': 'Tələb olunan komponentlər (Firebase və ya Llama modeli) işə salına bilmədiyi üçün tətbiq işləyə bilmir.',
                
                # Data field labels
                'restaurant_name': 'Restoran Adı',
                'description': 'Təsvir',
                'cuisine_types': 'Mətbəx Növləri',
                'address': 'Ünvan',
                'services': 'Xidmətlər',
                'features': 'Xüsusiyyətlər',
                'atmosphere': 'Atmosfer',
                'menu_item': 'Menyu Elementi',
                'category': 'Kateqoriya',
                'price': 'Qiymət',
                'ingredients': 'Tərkib',
                'allergens': 'Allergenlər',
                'unknown_restaurant': 'Naməlum Restoran',
                'unknown_product': 'Naməlum Məhsul',
                'not_specified': 'Göstərilməyib',
                'none': 'Yoxdur',
                
                # Error responses
                'no_answer_found': 'Bu mövzuda təqdim edilən məlumatlarda cavab tapa bilmədim.',
                'no_data_available': 'Xəta: Mövcud məlumat yoxdur.',
                'data_load_failed': 'Xəta: Firestore-dan məlumatlar yüklənə bilmədi.',
                'sorry_no_info': 'Təəssüf ki, sualınızla bağlı spesifik məlumat məlumat bazamda tapa bilmədim.',
                
                # Prompt templates
                'system_prompt': """Siz restoranlar, menyular və yeməklər haqqında sualları cavablayan köməkçi assistansınız.
Yalnız aşağıda 'Təqdim edilən məlumat' bölməsində verilən mətnləri istifadə edərək cavab verin.
Əgər cavab bu məlumatlarda yoxdursa, "Bu mövzuda təqdim edilən məlumatlarda cavab tapa bilmədim." deyin.
Cavablarınızı qısa və dəqiq tutun. Təxmin etməyin. Təqdim edilən məlumatların xaricinə çıxmayın.
Sizə verilən məlumatlardakı restoran adı, menyu elementi adı kimi xüsusi adları düzgün istifadə edin.
Cavabınızın sonunda hansı məlumatı istifadə etdiyinizi qeyd etməyin (məsələn, "Məlumat 1-ə görə..."). Birbaşa cavab verin.""",
                'provided_info': 'Təqdim edilən məlumat',
                'question': 'Sual'
            }
        }
    
    def get_text(self, key: str, language: str = 'en', **kwargs) -> str:
        """Get translated text for a given key and language"""
        if language not in self.translations:
            language = 'en'  # Fallback to English
            
        text = self.translations[language].get(key, self.translations['en'].get(key, key))
        
        # Format with provided arguments
        if kwargs:
            try:
                return text.format(**kwargs)
            except (KeyError, ValueError):
                return text
        return text
    
    def get_supported_languages(self) -> list:
        """Get list of supported languages"""
        return list(self.translations.keys())
