import re
import math
import logging
from typing import List, Dict, Any, Set, Tuple
from collections import Counter, defaultdict
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class EnhancedRetriever:
    """Enhanced retrieval system with TF-IDF and semantic matching"""
    
    def __init__(self, cache_ttl_minutes: int = 30):
        self.cache_ttl_minutes = cache_ttl_minutes
        self.tfidf_cache = {}
        self.cache_timestamp = None
        self.stop_words = self._get_stop_words()
        
    def _get_stop_words(self) -> Set[str]:
        """Get stop words for multiple languages"""
        return {
            # English stop words
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them',
            # Turkish stop words
            'bir', 've', 'bu', 'da', 'de', 'ile', 'için', 'var', 'yok', 'olan', 'olarak', 'gibi', 'kadar', 'daha', 'en', 'çok', 'az', 'büyük', 'küçük', 'iyi', 'kötü', 'yeni', 'eski', 'ben', 'sen', 'o', 'biz', 'siz', 'onlar', 'şu', 'şey', 'ne', 'nasıl', 'nerede', 'neden', 'kim', 'hangi',
            # Azerbaijani stop words
            'bir', 'və', 'bu', 'da', 'də', 'ilə', 'üçün', 'var', 'yox', 'olan', 'olaraq', 'kimi', 'qədər', 'daha', 'ən', 'çox', 'az', 'böyük', 'kiçik', 'yaxşı', 'pis', 'yeni', 'köhnə', 'mən', 'sən', 'o', 'biz', 'siz', 'onlar', 'şu', 'şey', 'nə', 'necə', 'harada', 'niyə', 'kim', 'hansı'
        }
    
    def _preprocess_text(self, text: str) -> List[str]:
        """Preprocess text by tokenizing and removing stop words"""
        # Convert to lowercase and remove special characters
        text = re.sub(r'[^\w\s]', ' ', text.lower())
        
        # Tokenize
        tokens = text.split()
        
        # Remove stop words and short tokens
        tokens = [token for token in tokens if len(token) > 2 and token not in self.stop_words]
        
        return tokens
    
    def _calculate_tfidf(self, documents: List[Dict[str, Any]]) -> Dict[str, Dict[str, float]]:
        """Calculate TF-IDF scores for all documents"""
        if self._is_cache_valid():
            return self.tfidf_cache
        
        logger.info("Calculating TF-IDF scores for documents...")
        
        # Preprocess all documents
        doc_tokens = {}
        all_tokens = set()
        
        for doc in documents:
            doc_id = doc['id']
            tokens = self._preprocess_text(doc['text'])
            doc_tokens[doc_id] = tokens
            all_tokens.update(tokens)
        
        # Calculate TF-IDF
        tfidf_scores = {}
        total_docs = len(documents)
        
        # Calculate document frequency for each term
        df = defaultdict(int)
        for tokens in doc_tokens.values():
            unique_tokens = set(tokens)
            for token in unique_tokens:
                df[token] += 1
        
        # Calculate TF-IDF for each document
        for doc_id, tokens in doc_tokens.items():
            tfidf_scores[doc_id] = {}
            token_counts = Counter(tokens)
            total_tokens = len(tokens)
            
            for token in set(tokens):
                # Term frequency
                tf = token_counts[token] / total_tokens
                
                # Inverse document frequency
                idf = math.log(total_docs / (df[token] + 1))
                
                # TF-IDF score
                tfidf_scores[doc_id][token] = tf * idf
        
        # Cache the results
        self.tfidf_cache = tfidf_scores
        self.cache_timestamp = datetime.now()
        
        logger.info(f"TF-IDF calculation completed for {total_docs} documents")
        return tfidf_scores
    
    def _is_cache_valid(self) -> bool:
        """Check if TF-IDF cache is still valid"""
        if not self.cache_timestamp or not self.tfidf_cache:
            return False
        
        time_diff = datetime.now() - self.cache_timestamp
        return time_diff < timedelta(minutes=self.cache_ttl_minutes)
    
    def _calculate_similarity_score(self, query_tokens: List[str], doc_id: str, 
                                  tfidf_scores: Dict[str, Dict[str, float]], 
                                  doc: Dict[str, Any]) -> float:
        """Calculate similarity score between query and document"""
        score = 0.0
        
        # TF-IDF based scoring
        doc_tfidf = tfidf_scores.get(doc_id, {})
        for token in query_tokens:
            if token in doc_tfidf:
                score += doc_tfidf[token]
        
        # Boost score for exact phrase matches
        query_text = ' '.join(query_tokens)
        doc_text = doc['text'].lower()
        if query_text in doc_text:
            score += 2.0
        
        # Boost score for metadata matches
        metadata = doc.get('metadata', {})
        
        # Restaurant name match
        restaurant_name = metadata.get('name', '').lower()
        if restaurant_name:
            for token in query_tokens:
                if token in restaurant_name:
                    score += 1.5
        
        # Menu item name match
        item_name = metadata.get('item_name', '').lower()
        if item_name:
            for token in query_tokens:
                if token in item_name:
                    score += 1.5
        
        # Restaurant name in query boost
        restaurant_name_in_query = metadata.get('restaurant_name', '').lower()
        if restaurant_name_in_query:
            for token in query_tokens:
                if token in restaurant_name_in_query:
                    score += 1.0
        
        # Type-specific boosts
        doc_type = metadata.get('type', '')
        if 'restoran' in query_text or 'restaurant' in query_text:
            if doc_type == 'restaurant_info':
                score += 0.5
        elif 'menü' in query_text or 'menu' in query_text or 'yemek' in query_text or 'food' in query_text:
            if doc_type == 'menu_item':
                score += 0.5
        
        return score
    
    def retrieve(self, query: str, documents: List[Dict[str, Any]], top_n: int = 3) -> List[Dict[str, Any]]:
        """Retrieve most relevant documents for a query"""
        if not documents:
            logger.warning("No documents provided for retrieval")
            return []
        
        if not query.strip():
            logger.warning("Empty query provided")
            return []
        
        # Preprocess query
        query_tokens = self._preprocess_text(query)
        if not query_tokens:
            logger.warning("No valid tokens found in query after preprocessing")
            return []
        
        # Calculate TF-IDF scores
        tfidf_scores = self._calculate_tfidf(documents)
        
        # Score all documents
        scored_docs = []
        for doc in documents:
            score = self._calculate_similarity_score(query_tokens, doc['id'], tfidf_scores, doc)
            if score > 0:
                scored_docs.append({
                    'document': doc,
                    'score': score
                })
        
        # Sort by score and return top N
        scored_docs.sort(key=lambda x: x['score'], reverse=True)
        
        logger.info(f"Retrieved {len(scored_docs)} relevant documents for query '{query}', returning top {top_n}")
        
        return [item['document'] for item in scored_docs[:top_n]]
    
    def clear_cache(self):
        """Clear the TF-IDF cache"""
        self.tfidf_cache = {}
        self.cache_timestamp = None
        logger.info("TF-IDF cache cleared")


class KeywordRetriever:
    """Simple keyword-based retriever (fallback option)"""
    
    def __init__(self):
        pass
    
    def retrieve(self, query: str, documents: List[Dict[str, Any]], top_n: int = 3) -> List[Dict[str, Any]]:
        """Simple keyword-based retrieval"""
        query_lower = query.lower()
        query_words = set(query_lower.split())
        
        scored_documents = []
        
        for doc in documents:
            doc_text_lower = doc["text"].lower()
            score = 0
            
            # Basic word matching score
            matched_words = 0
            for word in query_words:
                if word in doc_text_lower:
                    matched_words += 1
                    score += doc_text_lower.count(word)
            
            if matched_words == 0:
                continue
            
            score += matched_words * 5
            
            # Metadata matching bonus
            metadata = doc.get("metadata", {})
            doc_name_lower = metadata.get("name", "").lower()
            doc_item_name_lower = metadata.get("item_name", "").lower()
            
            if doc_name_lower and any(q_word in doc_name_lower for q_word in query_words.union({query_lower})):
                score += 20
            if doc_item_name_lower and any(q_word in doc_item_name_lower for q_word in query_words.union({query_lower})):
                score += 20
            
            # Query substring bonus
            if query_lower in doc_text_lower:
                score += 15
            
            if score > 0:
                scored_documents.append({"score": score, "document": doc})
        
        scored_documents.sort(key=lambda x: x["score"], reverse=True)
        return [item["document"] for item in scored_documents[:top_n]]
