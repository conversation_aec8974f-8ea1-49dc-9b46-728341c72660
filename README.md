# Enhanced RAG Application with Multi-Language Support

This is an enhanced version of the RAG (Retrieval-Augmented Generation) application that provides restaurant and menu information with support for multiple languages (English, Turkish, and Azerbaijani).

## 🚀 Key Improvements

### 1. **Multi-Language Support (i18n)**
- **English**, **Turkish**, and **Azerbaijani** language support
- Automatic language detection based on query content
- Localized prompts, error messages, and system responses
- Language-specific data formatting

### 2. **Performance Enhancements**
- **Enhanced TF-IDF Retrieval**: Improved document retrieval using TF-IDF scoring with semantic matching
- **Caching System**: Intelligent caching with TTL for both data and TF-IDF calculations
- **Parallel Processing**: Multi-threaded data loading from Firestore
- **Connection Pooling**: Optimized Firestore connections
- **Batch Processing**: Efficient handling of large datasets

### 3. **Accuracy Improvements**
- **Advanced Retrieval Algorithm**: TF-IDF + semantic similarity scoring
- **Query Preprocessing**: Text normalization and stop word removal
- **Metadata Boosting**: Enhanced scoring for restaurant and menu item names
- **Fallback Mechanisms**: Graceful degradation with keyword-based retrieval
- **Better Prompt Engineering**: Language-specific prompts for improved responses

### 4. **Code Architecture**
- **Modular Design**: Separated concerns into dedicated modules
- **Configuration Management**: Centralized configuration with environment variable support
- **Enhanced Error Handling**: Comprehensive error handling with localized messages
- **Logging**: Structured logging with different levels
- **Type Hints**: Full type annotation for better code maintainability

## 📁 Project Structure

```
backend/
├── rag_firestore_app.py      # Main application file
├── config.py                 # Configuration management
├── i18n.py                   # Internationalization support
├── retriever.py              # Enhanced retrieval algorithms
├── llm_handler.py            # LLM interaction handling
├── firestore_client.py       # Firestore operations
├── requirements.txt          # Python dependencies
├── test_app.py              # Test script
├── README.md                # This file
├── serviceKey.json          # Firebase service account key
└── Meta-Llama-3-8B-Instruct.Q4_K_S.gguf  # LLM model file
```

## 🛠 Installation

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set up environment variables (optional):**
   ```bash
   export SERVICE_ACCOUNT_FILE="./serviceKey.json"
   export LLAMA_MODEL_PATH="./Meta-Llama-3-8B-Instruct.Q4_K_S.gguf"
   export DEFAULT_LANGUAGE="en"
   export LOG_LEVEL="INFO"
   ```

3. **Run the application:**
   ```bash
   python rag_firestore_app.py
   ```

## 🌐 API Endpoints

### 1. Chat Endpoint
**POST** `/chat`

```json
{
  "query": "What restaurants do you have?",
  "language": "en"  // Optional: en, tr, az
}
```

**Response:**
```json
{
  "response": "Based on the provided information, we have several restaurants...",
  "detected_language": "en",
  "query": "What restaurants do you have?"
}
```

### 2. Reload Data
**POST** `/reload_data`

```json
{
  "language": "en"  // Optional
}
```

### 3. System Status
**GET** `/status`

Returns system status including component initialization status and cache information.

### 4. Health Check
**GET** `/health`

Simple health check endpoint.

## 🌍 Language Support

### Supported Languages:
- **English (en)**: Default language
- **Turkish (tr)**: Full localization
- **Azerbaijani (az)**: Full localization

### Language Detection:
The system automatically detects the language based on:
- Character patterns (ç, ğ, ı, ö, ş, ü, ə)
- Common words in each language
- Fallback to English if detection fails

### Example Queries:

**English:**
```
"What restaurants do you have?"
"Tell me about the pizza menu"
```

**Turkish:**
```
"Hangi restoranlar var?"
"Pizza menüsü hakkında bilgi ver"
```

**Azerbaijani:**
```
"Hansı restoranlar var?"
"Pizza menyusu haqqında məlumat ver"
```

## ⚡ Performance Features

### Enhanced Retrieval:
- **TF-IDF Scoring**: Statistical relevance scoring
- **Semantic Matching**: Context-aware document matching
- **Metadata Boosting**: Restaurant and menu name prioritization
- **Caching**: 30-minute TTL for TF-IDF calculations

### Optimizations:
- **Parallel Data Loading**: Multi-threaded Firestore operations
- **Batch Processing**: Efficient handling of large document sets
- **Connection Pooling**: Optimized database connections
- **Memory Management**: Intelligent caching with automatic cleanup

## 🧪 Testing

Run the test script to verify functionality:

```bash
python test_app.py
```

The test script will:
- Check health and status endpoints
- Test data reloading
- Test chat functionality in multiple languages
- Verify language auto-detection

## 🔧 Configuration

### Environment Variables:

| Variable | Default | Description |
|----------|---------|-------------|
| `SERVICE_ACCOUNT_FILE` | `./serviceKey.json` | Firebase service account file |
| `LLAMA_MODEL_PATH` | `./Meta-Llama-3-8B-Instruct.Q4_K_S.gguf` | LLM model file path |
| `DEFAULT_LANGUAGE` | `en` | Default language |
| `LOG_LEVEL` | `INFO` | Logging level |
| `FLASK_HOST` | `0.0.0.0` | Flask host |
| `FLASK_PORT` | `5000` | Flask port |
| `CACHE_TTL_MINUTES` | `30` | Cache TTL in minutes |
| `TOP_N_DOCUMENTS` | `3` | Number of documents to retrieve |

### Advanced Configuration:
- **LLM Parameters**: Temperature, top_p, max_tokens
- **Retrieval Settings**: TF-IDF weights, similarity thresholds
- **Performance Tuning**: Thread pool size, batch size

## 🚀 Production Deployment

For production deployment:

1. **Use Gunicorn:**
   ```bash
   gunicorn -w 4 -b 0.0.0.0:5000 rag_firestore_app:app
   ```

2. **Set production environment variables:**
   ```bash
   export FLASK_DEBUG=False
   export LOG_LEVEL=WARNING
   ```

3. **Configure reverse proxy** (nginx, Apache)

## 📊 Monitoring

The application provides:
- **Health checks** at `/health`
- **System status** at `/status`
- **Structured logging** with timestamps
- **Performance metrics** in cache info

## 🤝 Contributing

1. Follow the modular architecture
2. Add type hints for new functions
3. Update i18n translations for new messages
4. Add tests for new functionality
5. Update documentation

## 📝 License

This project is licensed under the MIT License.
