import os
from typing import Dict, Any

class Config:
    """Configuration management for the RAG application"""
    
    def __init__(self):
        # Firebase Configuration
        self.SERVICE_ACCOUNT_FILE = os.getenv('SERVICE_ACCOUNT_FILE', './serviceKey.json')
        
        # Llama Model Configuration
        self.LLAMA_MODEL_PATH = os.getenv('LLAMA_MODEL_PATH', './Meta-Llama-3-8B-Instruct.Q4_K_S.gguf')
        self.LLAMA_N_CTX = int(os.getenv('LLAMA_N_CTX', '4096'))
        self.LLAMA_N_GPU_LAYERS = int(os.getenv('LLAMA_N_GPU_LAYERS', '-1'))
        self.LLAMA_VERBOSE = os.getenv('LLAMA_VERBOSE', 'True').lower() == 'true'
        
        # LLM Generation Parameters
        self.MAX_TOKENS = int(os.getenv('MAX_TOKENS', '350'))
        self.TEMPERATURE = float(os.getenv('TEMPERATURE', '0.2'))
        self.TOP_P = float(os.getenv('TOP_P', '0.9'))
        
        # Retrieval Configuration
        self.TOP_N_DOCUMENTS = int(os.getenv('TOP_N_DOCUMENTS', '3'))
        self.CACHE_TTL_MINUTES = int(os.getenv('CACHE_TTL_MINUTES', '30'))
        
        # Application Configuration
        self.DEFAULT_LANGUAGE = os.getenv('DEFAULT_LANGUAGE', 'en')
        self.SUPPORTED_LANGUAGES = ['en', 'tr', 'az']
        self.LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
        self.FLASK_HOST = os.getenv('FLASK_HOST', '0.0.0.0')
        self.FLASK_PORT = int(os.getenv('FLASK_PORT', '5000'))
        self.FLASK_DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
        
        # Performance Configuration
        self.THREAD_POOL_SIZE = int(os.getenv('THREAD_POOL_SIZE', '4'))
        self.BATCH_SIZE = int(os.getenv('BATCH_SIZE', '100'))
        
        # Stop tokens for LLM
        self.STOP_TOKENS = [
            "<|eot_id|>", 
            "<|end_of_text|>", 
            "user:", 
            "\nSoru:", 
            "\nQuestion:", 
            "\nSual:",
            "Sağlanan Bilgiler:",
            "Provided Information:",
            "Təqdim edilən məlumat:"
        ]
    
    def get_language_config(self, language: str) -> Dict[str, Any]:
        """Get language-specific configuration"""
        if language not in self.SUPPORTED_LANGUAGES:
            language = self.DEFAULT_LANGUAGE
            
        return {
            'language': language,
            'is_default': language == self.DEFAULT_LANGUAGE
        }
    
    def validate_config(self) -> bool:
        """Validate configuration settings"""
        errors = []
        
        # Check required files
        if not os.path.exists(self.SERVICE_ACCOUNT_FILE):
            errors.append(f"Service account file not found: {self.SERVICE_ACCOUNT_FILE}")
            
        if not os.path.exists(self.LLAMA_MODEL_PATH):
            errors.append(f"Llama model file not found: {self.LLAMA_MODEL_PATH}")
        
        # Check numeric ranges
        if self.TEMPERATURE < 0 or self.TEMPERATURE > 2:
            errors.append("Temperature must be between 0 and 2")
            
        if self.TOP_P < 0 or self.TOP_P > 1:
            errors.append("Top_p must be between 0 and 1")
            
        if self.TOP_N_DOCUMENTS < 1:
            errors.append("TOP_N_DOCUMENTS must be at least 1")
        
        if errors:
            for error in errors:
                print(f"Configuration Error: {error}")
            return False
            
        return True
