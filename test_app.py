#!/usr/bin/env python3
"""
Test script for the enhanced RAG application
"""

import requests
import time

# Configuration
BASE_URL = "http://localhost:5000"

def test_health_check():
    """Test the health check endpoint"""
    print("Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"Health check status: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Health check failed: {e}")
        return False

def test_status():
    """Test the status endpoint"""
    print("\nTesting status endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/status")
        print(f"Status check: {response.status_code}")
        if response.status_code == 200:
            status = response.json()
            print(f"Firestore initialized: {status['firestore']['initialized']}")
            print(f"LLM loaded: {status['llm']['loaded']}")
            print(f"Retriever initialized: {status['retriever']['initialized']}")
            print(f"Supported languages: {status['supported_languages']}")
        return response.status_code == 200
    except Exception as e:
        print(f"Status check failed: {e}")
        return False

def test_chat(query, language=None):
    """Test the chat endpoint"""
    print(f"\nTesting chat with query: '{query}' (language: {language})")
    try:
        payload = {"query": query}
        if language:
            payload["language"] = language

        response = requests.post(
            f"{BASE_URL}/chat",
            json=payload,
            headers={"Content-Type": "application/json"}
        )

        print(f"Chat response status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Detected language: {result.get('detected_language')}")
            print(f"Response: {result.get('response')}")
        else:
            print(f"Error: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Chat test failed: {e}")
        return False

def test_reload_data(language="en"):
    """Test the data reload endpoint"""
    print(f"\nTesting data reload (language: {language})...")
    try:
        response = requests.post(
            f"{BASE_URL}/reload_data",
            json={"language": language},
            headers={"Content-Type": "application/json"}
        )

        print(f"Reload status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Message: {result.get('message')}")
            print(f"Cache info: {result.get('cache_info')}")
        else:
            print(f"Error: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Reload test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Starting RAG Application Tests")
    print("=" * 50)

    # Wait for server to start
    print("Waiting for server to start...")
    time.sleep(2)

    # Test health check
    if not test_health_check():
        print("Health check failed. Is the server running?")
        return

    # Test status
    if not test_status():
        print("Status check failed.")
        return

    # Test data reload
    test_reload_data("en")

    # Test chat in different languages
    test_queries = [
        ("What restaurants do you have?", "en"),
        ("Hangi restoranlar var?", "tr"),
        ("Hansı restoranlar var?", "az"),
        ("Tell me about the menu", None),  # Auto-detect
        ("Pizza var mı?", None),  # Auto-detect Turkish
    ]

    for query, language in test_queries:
        test_chat(query, language)
        time.sleep(1)  # Small delay between requests

    print("\n" + "=" * 50)
    print("Tests completed!")

if __name__ == "__main__":
    main()
