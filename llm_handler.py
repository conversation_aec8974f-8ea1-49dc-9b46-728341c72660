import logging
from typing import List, Dict, Any, Optional
from llama_cpp import <PERSON>lam<PERSON>
from i18n import I18nManager

logger = logging.getLogger(__name__)

class LLMHandler:
    """Handler for Llama model interactions with multi-language support"""

    def __init__(self, model_path: str, n_ctx: int = 4096, n_gpu_layers: int = -1,
                 verbose: bool = True, i18n_manager: Optional[I18nManager] = None):
        self.model_path = model_path
        self.n_ctx = n_ctx
        self.n_gpu_layers = n_gpu_layers
        self.verbose = verbose
        self.llm = None
        self.i18n = i18n_manager or I18nManager()

    def load_model(self) -> bool:
        """Load the Llama model"""
        try:
            import os
            if not os.path.exists(self.model_path):
                error_msg = self.i18n.get_text('llama_model_not_found', 'en', path=self.model_path)
                logger.error(error_msg)
                raise FileNotFoundError(self.i18n.get_text('llama_model_missing', 'en', path=self.model_path))

            self.llm = Llama(
                model_path=self.model_path,
                n_ctx=self.n_ctx,
                n_gpu_layers=self.n_gpu_layers,
                verbose=self.verbose
            )

            success_msg = self.i18n.get_text('llama_model_loaded', 'en', path=self.model_path)
            logger.info(success_msg)
            return True

        except Exception as e:
            error_msg = self.i18n.get_text('llama_model_error', 'en', error=str(e))
            logger.error(error_msg, exc_info=True)
            return False

    def is_loaded(self) -> bool:
        """Check if model is loaded"""
        return self.llm is not None

    def construct_prompt(self, query: str, context_documents: List[Dict[str, Any]],
                        language: str = 'en') -> str:
        """Construct prompt for the LLM based on language"""
        # Format context documents
        context_parts = []
        provided_info_label = self.i18n.get_text('provided_info', language)

        for i, doc in enumerate(context_documents):
            context_parts.append(f"{provided_info_label} {i+1}:\n{doc['text']}")

        context_str = "\n\n".join(context_parts)

        # Get system prompt and labels
        system_prompt = self.i18n.get_text('system_prompt', language)
        question_label = self.i18n.get_text('question', language)

        # Construct the full prompt
        prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>
{system_prompt}<|eot_id|><|start_header_id|>user<|end_header_id|>
{provided_info_label}:
{context_str}

{question_label}: {query}<|eot_id|><|start_header_id|>assistant<|end_header_id|>
"""
        return prompt

    def generate_response(self, query: str, context_documents: List[Dict[str, Any]],
                         language: str = 'en', max_tokens: int = 350,
                         temperature: float = 0.2, top_p: float = 0.9,
                         stop_tokens: List[str] = None) -> str:
        """Generate response using the LLM"""
        if not self.is_loaded():
            return self.i18n.get_text('llm_not_loaded', language)

        # Default stop tokens if none provided
        if stop_tokens is None:
            stop_tokens = [
                "<|eot_id|>",
                "<|end_of_text|>",
                "user:",
                "\nSoru:",
                "\nQuestion:",
                "\nSual:",
                "Sağlanan Bilgiler:",
                "Provided Information:",
                "Təqdim edilən məlumat:"
            ]

        # Construct prompt
        prompt = self.construct_prompt(query, context_documents, language)

        logger.debug(f"Generated prompt for LLM:\n{prompt}")

        try:
            response = self.llm(
                prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=top_p,
                stop=stop_tokens
            )

            generated_text = response["choices"][0]["text"].strip()

            # Post-process the response
            generated_text = self._post_process_response(generated_text, language)

            logger.info(self.i18n.get_text('llm_response_generated', 'en', response=generated_text))
            return generated_text

        except Exception as e:
            error_msg = self.i18n.get_text('llm_response_error', 'en', error=str(e))
            logger.error(error_msg, exc_info=True)
            return self.i18n.get_text('llm_generation_error', language)

    def _post_process_response(self, response: str, language: str) -> str:
        """Post-process the generated response"""
        # Remove any remaining stop tokens
        stop_patterns = [
            "<|eot_id|>",
            "<|end_of_text|>",
            "user:",
            "\nSoru:",
            "\nQuestion:",
            "\nSual:",
            "Sağlanan Bilgiler:",
            "Provided Information:",
            "Təqdim edilən məlumat:"
        ]

        for pattern in stop_patterns:
            if pattern in response:
                response = response.split(pattern)[0]

        # Clean up extra whitespace
        response = response.strip()

        # If response is empty or too short, provide fallback
        if len(response.strip()) < 10:
            return self.i18n.get_text('no_answer_found', language)

        return response

    def generate_response_with_fallback(self, query: str, context_documents: List[Dict[str, Any]],
                                      language: str = 'en', **kwargs) -> str:
        """Generate response with fallback handling"""
        if not context_documents:
            logger.info(f"No context documents provided for query: {query}")
            return self.i18n.get_text('sorry_no_info', language)

        try:
            response = self.generate_response(query, context_documents, language, **kwargs)

            # Check if response is meaningful
            if len(response.strip()) < 10:
                return self.i18n.get_text('no_answer_found', language)

            return response

        except Exception as e:
            logger.error(f"Error in generate_response_with_fallback: {e}", exc_info=True)
            return self.i18n.get_text('llm_generation_error', language)

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model"""
        if not self.is_loaded():
            return {"loaded": False}

        return {
            "loaded": True,
            "model_path": self.model_path,
            "n_ctx": self.n_ctx,
            "n_gpu_layers": self.n_gpu_layers,
            "verbose": self.verbose
        }

    def unload_model(self):
        """Unload the model to free memory"""
        if self.llm:
            del self.llm
            self.llm = None
            logger.info("Llama model unloaded")


class PromptTemplate:
    """Template class for managing different prompt formats"""

    def __init__(self, i18n_manager: I18nManager):
        self.i18n = i18n_manager

    def get_system_prompt(self, language: str, domain: str = 'restaurant') -> str:
        """Get system prompt for specific domain and language"""
        if domain == 'restaurant':
            return self.i18n.get_text('system_prompt', language)

        # Add more domain-specific prompts here if needed
        return self.i18n.get_text('system_prompt', language)

    def format_context(self, documents: List[Dict[str, Any]], language: str) -> str:
        """Format context documents for the prompt"""
        if not documents:
            return ""

        provided_info_label = self.i18n.get_text('provided_info', language)
        context_parts = []

        for i, doc in enumerate(documents):
            context_parts.append(f"{provided_info_label} {i+1}:\n{doc['text']}")

        return "\n\n".join(context_parts)

    def create_chat_prompt(self, query: str, context: str, language: str) -> str:
        """Create a complete chat prompt"""
        system_prompt = self.get_system_prompt(language)
        question_label = self.i18n.get_text('question', language)
        provided_info_label = self.i18n.get_text('provided_info', language)

        return f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>
{system_prompt}<|eot_id|><|start_header_id|>user<|end_header_id|>
{provided_info_label}:
{context}

{question_label}: {query}<|eot_id|><|start_header_id|>assistant<|end_header_id|>
"""
