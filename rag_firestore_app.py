import logging
from typing import List, Dict, Any
from flask import Flask, request, jsonify
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime

# Import custom modules
from config import Config
from i18n import I18nManager
from retriever import EnhancedRetriever
from llm_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from firestore_client import FirestoreClient

# --- Configuration ---
config = Config()
i18n = I18nManager()

# --- Logger Setup ---
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# --- Global Variables ---
firestore_client = None
llm_handler = None
retriever = None
executor = ThreadPoolExecutor(max_workers=4)

# --- Application Initialization ---
def initialize_components() -> bool:
    """Initialize all application components"""
    global firestore_client, llm_handler, retriever

    # Validate configuration
    if not config.validate_config():
        logger.error("Configuration validation failed")
        return False

    # Initialize Firestore client
    firestore_client = FirestoreClient(
        service_account_file=config.SERVICE_ACCOUNT_FILE,
        i18n_manager=i18n,
        cache_ttl_minutes=config.CACHE_TTL_MINUTES,
        batch_size=config.BATCH_SIZE
    )

    if not firestore_client.initialize():
        logger.error("Failed to initialize Firestore client")
        return False

    # Initialize LLM handler
    llm_handler = LLMHandler(
        model_path=config.LLAMA_MODEL_PATH,
        n_ctx=config.LLAMA_N_CTX,
        n_gpu_layers=config.LLAMA_N_GPU_LAYERS,
        verbose=config.LLAMA_VERBOSE,
        i18n_manager=i18n
    )

    if not llm_handler.load_model():
        logger.error("Failed to load LLM model")
        return False

    # Initialize retriever
    retriever = EnhancedRetriever(cache_ttl_minutes=config.CACHE_TTL_MINUTES)

    logger.info("All components initialized successfully")
    return True

# --- Data Loading Functions ---
def load_data_with_language(language: str = 'en') -> List[Dict[str, Any]]:
    """Load data from Firestore with language support"""
    global firestore_client

    if not firestore_client or not firestore_client.is_initialized():
        logger.error(i18n.get_text('firestore_client_not_initialized', language))
        return []

    return firestore_client.load_data(language=language)

def get_cached_data() -> List[Dict[str, Any]]:
    """Get cached data from Firestore client"""
    global firestore_client

    if not firestore_client:
        return []

    return firestore_client.data_cache

def detect_language(query: str) -> str:
    """Simple language detection based on query content"""
    # Turkish indicators
    turkish_chars = set('çğıöşüÇĞIİÖŞÜ')
    turkish_words = {'restoran', 'menü', 'yemek', 'nerede', 'nasıl', 'ne', 'hangi', 'için', 'ile'}

    # Azerbaijani indicators
    azerbaijani_chars = set('əçğıöşüəÇĞIİÖŞÜ')
    azerbaijani_words = {'restoran', 'menyu', 'yemək', 'harada', 'necə', 'nə', 'hansı', 'üçün', 'ilə'}

    query_lower = query.lower()
    query_words = set(query_lower.split())

    # Check for Turkish
    if any(char in query for char in turkish_chars) or any(word in query_words for word in turkish_words):
        return 'tr'

    # Check for Azerbaijani
    if any(char in query for char in azerbaijani_chars) or any(word in query_words for word in azerbaijani_words):
        return 'az'

    # Default to English
    return 'en'

# --- Enhanced RAG Functions ---
def generate_response_with_rag(user_query: str, language: str = None) -> str:
    """Generate response using RAG with enhanced retrieval and multi-language support"""
    global llm_handler, firestore_client, retriever

    # Auto-detect language if not provided
    if language is None:
        language = detect_language(user_query)

    # Check if components are initialized
    if not llm_handler or not llm_handler.is_loaded():
        return i18n.get_text('llm_not_loaded', language)

    if not firestore_client or not firestore_client.is_initialized():
        return i18n.get_text('firestore_client_not_initialized', language)

    # Get data with language support
    documents = load_data_with_language(language)

    if not documents:
        logger.warning(i18n.get_text('firestore_cache_empty', language))
        # Try to reload data
        documents = firestore_client.load_data(language=language, use_cache=False)
        if not documents:
            return i18n.get_text('data_load_failed', language)

    # Retrieve relevant documents
    try:
        retrieved_docs = retriever.retrieve(user_query, documents, top_n=config.TOP_N_DOCUMENTS)
    except Exception as e:
        logger.error(f"Error in retrieval: {e}")
        # Fallback to simple keyword retriever
        from retriever import KeywordRetriever
        fallback_retriever = KeywordRetriever()
        retrieved_docs = fallback_retriever.retrieve(user_query, documents, top_n=config.TOP_N_DOCUMENTS)

    if not retrieved_docs:
        logger.info(i18n.get_text('no_relevant_docs', 'en', query=user_query))
        return i18n.get_text('sorry_no_info', language)

    # Generate response using LLM
    try:
        response = llm_handler.generate_response_with_fallback(
            query=user_query,
            context_documents=retrieved_docs,
            language=language,
            max_tokens=config.MAX_TOKENS,
            temperature=config.TEMPERATURE,
            top_p=config.TOP_P,
            stop_tokens=config.STOP_TOKENS
        )

        return response

    except Exception as e:
        logger.error(f"Error generating response: {e}", exc_info=True)
        return i18n.get_text('llm_generation_error', language)

# --- Utility Functions ---
def get_system_status() -> Dict[str, Any]:
    """Get system status information"""
    global firestore_client, llm_handler, retriever

    status = {
        "firestore": {
            "initialized": firestore_client is not None and firestore_client.is_initialized(),
            "cache_info": firestore_client.get_cache_info() if firestore_client else None
        },
        "llm": {
            "loaded": llm_handler is not None and llm_handler.is_loaded(),
            "model_info": llm_handler.get_model_info() if llm_handler else None
        },
        "retriever": {
            "initialized": retriever is not None,
            "type": "EnhancedRetriever" if retriever else None
        },
        "supported_languages": i18n.get_supported_languages(),
        "default_language": config.DEFAULT_LANGUAGE
    }

    return status

# --- Flask Application ---
app = Flask(__name__)

@app.route('/chat', methods=['POST'])
def chat_handler():
    """Enhanced chat handler with multi-language support"""
    try:
        data = request.get_json()
        user_query = data.get('query')
        language = data.get('language')  # Optional language parameter

        if not user_query:
            # Auto-detect language for error message
            detected_lang = detect_language(user_query) if user_query else config.DEFAULT_LANGUAGE
            error_msg = i18n.get_text('query_field_missing', detected_lang)
            return jsonify({"error": error_msg}), 400

        # Auto-detect language if not provided
        if not language:
            language = detect_language(user_query)

        logger.info(i18n.get_text('new_query_received', 'en', query=user_query))

        # Generate response with language support
        response_text = generate_response_with_rag(user_query, language)

        return jsonify({
            "response": response_text,
            "detected_language": language,
            "query": user_query
        })

    except Exception as e:
        error_msg = i18n.get_text('chat_request_error', 'en', error=str(e))
        logger.error(error_msg, exc_info=True)
        return jsonify({"error": i18n.get_text('server_error', config.DEFAULT_LANGUAGE)}), 500

@app.route('/reload_data', methods=['POST'])
def reload_data_handler():
    """Reload data from Firestore with language support"""
    try:
        data = request.get_json() or {}
        language = data.get('language', config.DEFAULT_LANGUAGE)

        logger.info(i18n.get_text('firestore_reload_request', language))

        global firestore_client
        if firestore_client and firestore_client.reload_data(language):
            cache_info = firestore_client.get_cache_info()
            success_msg = i18n.get_text('firestore_reload_success', language, count=cache_info['cache_size'])
            return jsonify({
                "message": success_msg,
                "cache_info": cache_info,
                "language": language
            }), 200
        else:
            error_msg = i18n.get_text('firestore_reload_error', language)
            return jsonify({"error": error_msg}), 500

    except Exception as e:
        logger.error(f"Error in reload_data_handler: {e}", exc_info=True)
        return jsonify({"error": i18n.get_text('server_error', config.DEFAULT_LANGUAGE)}), 500

@app.route('/status', methods=['GET'])
def status_handler():
    """Get system status"""
    try:
        status = get_system_status()
        return jsonify(status), 200
    except Exception as e:
        logger.error(f"Error in status_handler: {e}", exc_info=True)
        return jsonify({"error": "Failed to get system status"}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Simple health check endpoint"""
    return jsonify({"status": "healthy", "timestamp": datetime.now().isoformat()}), 200

# --- Main Execution Block ---
if __name__ == '__main__':
    logger.info(i18n.get_text('app_starting', config.DEFAULT_LANGUAGE))

    # Initialize all components
    if initialize_components():
        # Load initial data
        initial_data = load_data_with_language(config.DEFAULT_LANGUAGE)
        if not initial_data:
            logger.warning(i18n.get_text('app_startup_data_load_failed', config.DEFAULT_LANGUAGE))
        else:
            logger.info(f"Loaded {len(initial_data)} documents at startup")

        # Start Flask application
        logger.info(i18n.get_text('flask_app_starting', config.DEFAULT_LANGUAGE))
        app.run(
            host=config.FLASK_HOST,
            port=config.FLASK_PORT,
            debug=config.FLASK_DEBUG,
            threaded=True
        )
    else:
        logger.error(i18n.get_text('required_components_failed', config.DEFAULT_LANGUAGE))
