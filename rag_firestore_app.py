import os
import json
import logging
from flask import Flask, request, jsonify
import firebase_admin
from firebase_admin import credentials, firestore
from llama_cpp import Llama

# --- Ya<PERSON><PERSON><PERSON><PERSON><PERSON>ğişkenleri ---
SERVICE_ACCOUNT_FILE = './serviceKey.json'  # Firebase serviceKey dosyanızın yolu
LLAMA_MODEL_PATH = "./Meta-Llama-3-8B-Instruct.Q4_K_S.gguf" # Llama modelinizin .gguf dosya yolu
# Eğer modeliniz farklı bir dizindeyse, tam yolunu yazın, örn: "/path/to/your/model/Meta-Llama-3-8B-Instruct.Q4_K_S.gguf"

# --- Logger <PERSON>u ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Global Değişkenler ---
db = None
llm = None
firestore_data_cache = [] # Firestore'dan yüklenen veriler için basit bir önbellek

# --- Firebase Başlatma ---
def initialize_firebase():
    global db
    try:
        if not os.path.exists(SERVICE_ACCOUNT_FILE):
            logger.error(f"Firebase service account dosyası bulunamadı: {SERVICE_ACCOUNT_FILE}")
            raise FileNotFoundError(f"Service account dosyası '{SERVICE_ACCOUNT_FILE}' bulunamadı.")
        
        cred = credentials.Certificate(SERVICE_ACCOUNT_FILE)
        if not firebase_admin._apps: # Eğer uygulama zaten başlatılmadıysa başlat
            firebase_admin.initialize_app(cred)
        db = firestore.client()
        logger.info("Firebase Admin SDK başarıyla başlatıldı ve Firestore istemcisi oluşturuldu.")
        return True
    except Exception as e:
        logger.error(f"Firebase başlatılırken hata oluştu: {e}", exc_info=True)
        return False

# --- Llama Modelini Yükleme ---
def load_llama_model():
    global llm
    try:
        if not os.path.exists(LLAMA_MODEL_PATH):
            logger.error(f"Llama model dosyası bulunamadı: {LLAMA_MODEL_PATH}")
            raise FileNotFoundError(f"Llama model dosyası '{LLAMA_MODEL_PATH}' bulunamadı.")

        llm = Llama(
            model_path=LLAMA_MODEL_PATH,
            n_ctx=4096,       # Modele göre context penceresi boyutu
            n_gpu_layers=-1,  # Mümkünse tüm katmanları GPU'ya yükle (-1), CPU için 0
            verbose=True      # Yükleme sırasında detaylı bilgi göster
        )
        logger.info(f"Llama modeli '{LLAMA_MODEL_PATH}' başarıyla yüklendi.")
        return True
    except Exception as e:
        logger.error(f"Llama modeli yüklenirken hata oluştu: {e}", exc_info=True)
        return False

# --- Firestore'dan Veri Yükleme ve Ön İşleme ---
def load_and_preprocess_data_from_firestore():
    global firestore_data_cache
    if not db:
        logger.error("Firestore istemcisi başlatılmamış. Veri yüklenemiyor.")
        return False

    logger.info("Firestore'dan veri yükleniyor...")
    documents = []
    try:
        restaurants_ref = db.collection('restaurants')
        restaurants_docs = restaurants_ref.stream()

        for restaurant_doc in restaurants_docs:
            restaurant_data = restaurant_doc.to_dict()
            restaurant_id = restaurant_doc.id
            res_name = restaurant_data.get('restaurantName', f'Bilinmeyen Restoran ({restaurant_id})')

            # Restoran genel bilgileri
            res_info_parts = [
                f"Restoran Adı: {res_name}",
                f"Açıklama: {restaurant_data.get('description', 'Yok')}",
                f"Mutfak Türleri: {', '.join(restaurant_data.get('cuisines', [])) if restaurant_data.get('cuisines') else 'Belirtilmemiş'}",
                f"Adres: {restaurant_data.get('address', 'Yok')}",
                f"Hizmetler: {', '.join(restaurant_data.get('services', [])) if restaurant_data.get('services') else 'Belirtilmemiş'}",
                f"Özellikler: {', '.join(restaurant_data.get('features', [])) if restaurant_data.get('features') else 'Belirtilmemiş'}",
                f"Atmosfer: {', '.join(restaurant_data.get('atmosphere', [])) if restaurant_data.get('atmosphere') else 'Belirtilmemiş'}"
            ]
            documents.append({
                "id": f"restaurant_{restaurant_id}",
                "text": ". ".join(filter(None, res_info_parts)),
                "metadata": {"type": "restaurant_info", "restaurant_id": restaurant_id, "name": res_name}
            })

            # Menü öğeleri
            menu_ref = restaurant_doc.reference.collection('menu')
            menu_docs = menu_ref.stream()
            for menu_doc in menu_docs:
                item_data = menu_doc.to_dict()
                menu_id = menu_doc.id
                item_name = item_data.get('name', f'Bilinmeyen Ürün ({menu_id})')
                menu_item_parts = [
                    f"Menü Öğesi: {item_name} (Restoran: {res_name})",
                    f"Açıklama: {item_data.get('description', 'Yok')}",
                    f"Kategori: {item_data.get('category', 'Yok')}",
                    f"Fiyat: {item_data.get('price', 'Belirtilmemiş')}",
                    f"İçindekiler: {', '.join(item_data.get('ingredients', [])) if item_data.get('ingredients') else 'Belirtilmemiş'}",
                    f"Alerjenler: {', '.join(item_data.get('allergens', [])) if item_data.get('allergens') else 'Belirtilmemiş'}"
                ]
                documents.append({
                    "id": f"menu_{restaurant_id}_{menu_id}",
                    "text": ". ".join(filter(None, menu_item_parts)),
                    "metadata": {"type": "menu_item", "restaurant_id": restaurant_id, "menu_id": menu_id, "item_name": item_name, "restaurant_name": res_name}
                })
            
            # İsteğe bağlı: Yorumlar, siparişler vb. alt koleksiyonlar da eklenebilir.

        firestore_data_cache = documents
        logger.info(f"Firestore'dan {len(documents)} adet doküman başarıyla yüklendi ve işlendi.")
        return True
    except Exception as e:
        logger.error(f"Firestore'dan veri yüklenirken hata: {e}", exc_info=True)
        return False

# --- Basit Anahtar Kelime Tabanlı Retriever ---
def keyword_retriever(query, documents, top_n=3):
    query_lower = query.lower()
    query_words = set(query_lower.split())
    
    scored_documents = []

    for doc in documents:
        doc_text_lower = doc["text"].lower()
        score = 0
        
        # Temel kelime eşleşme skoru
        matched_words = 0
        for word in query_words:
            if word in doc_text_lower:
                matched_words +=1
                score += doc_text_lower.count(word) # Kelime frekansını da ekle
        
        if matched_words == 0: # Eğer hiçbir kelime eşleşmiyorsa devam etme
            continue

        score += matched_words * 5 # Eşleşen her benzersiz kelime için bonus

        # Metadata eşleşme bonusu
        doc_name_lower = doc["metadata"].get("name", "").lower() # Restoran adı
        doc_item_name_lower = doc["metadata"].get("item_name", "").lower() # Menü öğesi adı

        if doc_name_lower and any(q_word in doc_name_lower for q_word in query_words.union({query_lower})):
            score += 20 # Restoran adıyla daha güçlü eşleşme için büyük bonus
        if doc_item_name_lower and any(q_word in doc_item_name_lower for q_word in query_words.union({query_lower})):
            score += 20 # Menü öğesi adıyla daha güçlü eşleşme için büyük bonus
            
        # Sorgunun bir kısmı doküman metninde geçiyorsa bonus
        if query_lower in doc_text_lower:
            score += 15

        if score > 0:
            scored_documents.append({"score": score, "document": doc})

    scored_documents.sort(key=lambda x: x["score"], reverse=True)
    logger.info(f"Retriever, sorgu '{query}' için {len(scored_documents)} potansiyel doküman buldu, en iyi {top_n} tanesi seçildi.")
    return [item["document"] for item in scored_documents[:top_n]]


# --- Llama için Prompt Oluşturma ---
def construct_llama_prompt(query, context_documents):
    context_str = "\n\n".join([f"Sağlanan Bilgi {i+1}:\n{doc['text']}" for i, doc in enumerate(context_documents)])
    
    prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>
Sen Türkiye'deki restoranlar, menüler ve yemekler hakkında soruları yanıtlayan bir yardımcı asistansın.
Yalnızca aşağıda 'Sağlanan Bilgiler' bölümünde verilen metinleri kullanarak cevap ver.
Eğer cevap bu bilgilerde yoksa, "Bu konuda sağlanan bilgilerde bir cevap bulamadım." de.
Cevaplarını kısa ve öz tut. Tahminde bulunma. Sağlanan bilgilerin dışına çıkma.
Sana verilen bilgilerdeki restoran adı, menü öğesi adı gibi özel isimleri doğru kullan.
Cevabının sonunda hangi bilgiyi kullandığını belirtme (örneğin, "Bilgi 1'e göre..."). Direkt cevabı ver.<|eot_id|><|start_header_id|>user<|end_header_id|>
Sağlanan Bilgiler:
{context_str}

Soru: {query}<|eot_id|><|start_header_id|>assistant<|end_header_id|>
"""
    return prompt

# --- RAG ile Cevap Üretme ---
def generate_response_with_rag(user_query):
    global llm, firestore_data_cache
    if not llm:
        return "Hata: Llama modeli yüklenmemiş."
    if not firestore_data_cache: # Eğer önbellek boşsa (ilk istek veya yükleme hatası)
        logger.warning("Firestore veri önbelleği boş. Veriler yeniden yükleniyor.")
        if not load_and_preprocess_data_from_firestore():
             return "Hata: Firestore'dan veriler yüklenemedi."
        if not firestore_data_cache: # Yükleme sonrası hala boşsa
            return "Hata: Kullanılabilir veri yok."


    retrieved_docs = keyword_retriever(user_query, firestore_data_cache, top_n=3)

    if not retrieved_docs:
        logger.info(f"Sorgu '{user_query}' için ilgili doküman bulunamadı. LLM'e bağlam olmadan gidiliyor veya varsayılan cevap veriliyor.")
        # Bu durumda, LLM'e boş context ile gidebilir veya doğrudan "bilgi yok" diyebilirsiniz.
        # Şimdilik, LLM'in system prompt'taki talimatlara uymasını bekleyelim.
        # Alternatif olarak, burada "Üzgünüm, bu konuda spesifik bir bilgi bulamadım." dönebilirsiniz.
        # return "Üzgünüm, sorunuzla ilgili spesifik bir bilgi veritabanımda bulamadım."


    prompt_for_llm = construct_llama_prompt(user_query, retrieved_docs)
    logger.debug(f"LLM'e gönderilen prompt:\n{prompt_for_llm}")

    try:
        response = llm(
            prompt_for_llm,
            max_tokens=350,
            temperature=0.2, # Daha kesin ve olgusal cevaplar için düşük sıcaklık
            top_p=0.9,
            stop=["<|eot_id|>", "<|end_of_text|>", "user:", "\nSoru:", "Sağlanan Bilgiler:"] # Modelin durmasını istediğiniz tokenlar
        )
        generated_text = response["choices"][0]["text"].strip()
        logger.info(f"LLM'den üretilen cevap: {generated_text}")
        return generated_text
    except Exception as e:
        logger.error(f"LLM'den cevap üretilirken hata: {e}", exc_info=True)
        return "Modelden cevap üretilirken bir sorunla karşılaşıldı."

# --- Flask Uygulaması ---
app = Flask(__name__)

@app.route('/chat', methods=['POST'])
def chat_handler():
    try:
        data = request.get_json()
        user_query = data.get('query')

        if not user_query:
            return jsonify({"error": "Sorgu (query) alanı eksik."}), 400

        logger.info(f"Yeni sorgu alındı: {user_query}")
        response_text = generate_response_with_rag(user_query)
        
        return jsonify({"response": response_text})

    except Exception as e:
        logger.error(f"Chat isteği işlenirken hata: {e}", exc_info=True)
        return jsonify({"error": "İstek işlenirken sunucu hatası oluştu."}), 500

@app.route('/reload_data', methods=['POST'])
def reload_data_handler():
    logger.info("Firestore'dan verileri yeniden yükleme isteği alındı.")
    if load_and_preprocess_data_from_firestore():
        return jsonify({"message": f"Veriler Firestore'dan başarıyla yeniden yüklendi. {len(firestore_data_cache)} doküman işlendi."}), 200
    else:
        return jsonify({"error": "Veriler yeniden yüklenirken bir hata oluştu."}), 500

# --- Ana Çalıştırma Bloğu ---
if __name__ == '__main__':
    logger.info("Uygulama başlatılıyor...")
    firebase_ok = initialize_firebase()
    llama_ok = load_llama_model()

    if firebase_ok and llama_ok:
        # Uygulama başlangıcında verileri yükle
        if not load_and_preprocess_data_from_firestore():
            logger.warning("Başlangıçta Firestore verileri yüklenemedi. /reload_data endpoint'ini kullanmayı deneyin.")
        
        logger.info("Flask uygulaması 0.0.0.0:5000 adresinde başlatılıyor...")
        app.run(host='0.0.0.0', port=5000, debug=False) # debug=True geliştirme sırasında kullanılabilir
    else:
        logger.error("Gerekli bileşenler (Firebase veya Llama modeli) başlatılamadığı için uygulama çalıştırılamıyor.")
