#!/usr/bin/env python3
"""
Test script to verify component initialization
"""

import os
import sys

def test_config():
    """Test configuration loading"""
    print("🔧 Testing Configuration...")
    try:
        from config import Config
        config = Config()
        print(f"✅ Config loaded successfully")
        print(f"   - Default language: {config.DEFAULT_LANGUAGE}")
        print(f"   - Supported languages: {config.SUPPORTED_LANGUAGES}")
        print(f"   - Service account file: {config.SERVICE_ACCOUNT_FILE}")
        print(f"   - LLM model path: {config.LLAMA_MODEL_PATH}")
        return True
    except Exception as e:
        print(f"❌ Config failed: {e}")
        return False

def test_i18n():
    """Test internationalization"""
    print("\n🌍 Testing Internationalization...")
    try:
        from i18n import I18nManager
        i18n = I18nManager()
        
        # Test English
        en_msg = i18n.get_text('firebase_init_success', 'en')
        print(f"✅ English: {en_msg}")
        
        # Test Turkish
        tr_msg = i18n.get_text('firebase_init_success', 'tr')
        print(f"✅ Turkish: {tr_msg}")
        
        # Test Azerbaijani
        az_msg = i18n.get_text('firebase_init_success', 'az')
        print(f"✅ Azerbaijani: {az_msg}")
        
        return True
    except Exception as e:
        print(f"❌ I18n failed: {e}")
        return False

def test_language_detection():
    """Test language detection"""
    print("\n🔍 Testing Language Detection...")
    try:
        from rag_firestore_app import detect_language
        
        test_cases = [
            ("What restaurants do you have?", "en"),
            ("Hangi restoranlar var?", "tr"),
            ("Hansı restoranlar var?", "az"),
            ("Pizza var mı?", "tr"),
            ("Restoran haqqında məlumat", "az"),
            ("Tell me about the menu", "en")
        ]
        
        all_correct = True
        for query, expected in test_cases:
            detected = detect_language(query)
            status = "✅" if detected == expected else "❌"
            print(f"   {status} '{query}' → {detected} (expected: {expected})")
            if detected != expected:
                all_correct = False
        
        return all_correct
    except Exception as e:
        print(f"❌ Language detection failed: {e}")
        return False

def test_file_existence():
    """Test if required files exist"""
    print("\n📁 Testing File Existence...")
    
    files_to_check = [
        ("serviceKey.json", "Firebase service account"),
        ("Meta-Llama-3-8B-Instruct.Q4_K_S.gguf", "LLM model")
    ]
    
    all_exist = True
    for filename, description in files_to_check:
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            print(f"✅ {description}: {filename} ({size:,} bytes)")
        else:
            print(f"❌ {description}: {filename} (not found)")
            all_exist = False
    
    return all_exist

def test_retriever():
    """Test retriever initialization"""
    print("\n🔍 Testing Retriever...")
    try:
        from retriever import EnhancedRetriever, KeywordRetriever
        
        # Test enhanced retriever
        enhanced = EnhancedRetriever()
        print("✅ Enhanced retriever initialized")
        
        # Test keyword retriever (fallback)
        keyword = KeywordRetriever()
        print("✅ Keyword retriever initialized")
        
        # Test with sample data
        sample_docs = [
            {
                "id": "test1",
                "text": "This is a test restaurant with pizza and pasta",
                "metadata": {"type": "restaurant_info", "name": "Test Restaurant"}
            },
            {
                "id": "test2", 
                "text": "Pizza margherita with tomato and cheese",
                "metadata": {"type": "menu_item", "item_name": "Pizza Margherita"}
            }
        ]
        
        results = enhanced.retrieve("pizza", sample_docs, top_n=2)
        print(f"✅ Enhanced retrieval test: found {len(results)} documents")
        
        results = keyword.retrieve("pizza", sample_docs, top_n=2)
        print(f"✅ Keyword retrieval test: found {len(results)} documents")
        
        return True
    except Exception as e:
        print(f"❌ Retriever failed: {e}")
        return False

def test_firebase_connection():
    """Test Firebase connection (if credentials are valid)"""
    print("\n🔥 Testing Firebase Connection...")
    try:
        from firestore_client import FirestoreClient
        from i18n import I18nManager
        
        i18n = I18nManager()
        client = FirestoreClient("./serviceKey.json", i18n)
        
        if client.initialize():
            print("✅ Firebase connection successful")
            return True
        else:
            print("❌ Firebase connection failed (check credentials)")
            return False
    except Exception as e:
        print(f"❌ Firebase test failed: {e}")
        return False

def test_llm_handler():
    """Test LLM handler (if model file exists)"""
    print("\n🤖 Testing LLM Handler...")
    try:
        from llm_handler import LLMHandler
        from i18n import I18nManager
        
        i18n = I18nManager()
        handler = LLMHandler(
            model_path="./Meta-Llama-3-8B-Instruct.Q4_K_S.gguf",
            i18n_manager=i18n
        )
        
        if handler.load_model():
            print("✅ LLM model loaded successfully")
            return True
        else:
            print("❌ LLM model loading failed (check model file)")
            return False
    except Exception as e:
        print(f"❌ LLM test failed: {e}")
        return False

def main():
    """Run all component tests"""
    print("🧪 Enhanced RAG Application - Component Tests")
    print("=" * 50)
    
    tests = [
        ("Configuration", test_config),
        ("Internationalization", test_i18n),
        ("Language Detection", test_language_detection),
        ("File Existence", test_file_existence),
        ("Retriever", test_retriever),
        ("Firebase Connection", test_firebase_connection),
        ("LLM Handler", test_llm_handler),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed >= 5:  # Core functionality tests
        print("🎉 Core functionality is working! The application is ready to use.")
    elif passed >= 3:
        print("⚠️  Basic functionality is working. Check Firebase/LLM setup for full features.")
    else:
        print("❌ Multiple issues detected. Check your setup.")

if __name__ == "__main__":
    main()
