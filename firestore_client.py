import os
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import firebase_admin
from firebase_admin import credentials, firestore
from concurrent.futures import ThreadPoolExecutor, as_completed
from i18n import I18nManager

logger = logging.getLogger(__name__)

class FirestoreClient:
    """Enhanced Firestore client with caching and batch processing"""

    def __init__(self, service_account_file: str, i18n_manager: Optional[I18nManager] = None,
                 cache_ttl_minutes: int = 30, batch_size: int = 100):
        self.service_account_file = service_account_file
        self.i18n = i18n_manager or I18nManager()
        self.cache_ttl_minutes = cache_ttl_minutes
        self.batch_size = batch_size
        self.db = None
        self.data_cache = []
        self.cache_timestamp = None

    def initialize(self) -> bool:
        """Initialize Firebase connection"""
        try:
            if not os.path.exists(self.service_account_file):
                error_msg = self.i18n.get_text('firebase_file_not_found', 'en', file=self.service_account_file)
                logger.error(error_msg)
                raise FileNotFoundError(self.i18n.get_text('firebase_file_missing', 'en', file=self.service_account_file))

            cred = credentials.Certificate(self.service_account_file)
            if not firebase_admin._apps:
                firebase_admin.initialize_app(cred)

            self.db = firestore.client()

            success_msg = self.i18n.get_text('firebase_init_success', 'en')
            logger.info(success_msg)
            return True

        except Exception as e:
            error_msg = self.i18n.get_text('firebase_init_error', 'en', error=str(e))
            logger.error(error_msg, exc_info=True)
            return False

    def is_initialized(self) -> bool:
        """Check if Firestore client is initialized"""
        return self.db is not None

    def _is_cache_valid(self) -> bool:
        """Check if data cache is still valid"""
        if not self.cache_timestamp or not self.data_cache:
            return False

        time_diff = datetime.now() - self.cache_timestamp
        return time_diff < timedelta(minutes=self.cache_ttl_minutes)

    def _process_restaurant_data(self, restaurant_doc, language: str = 'en') -> List[Dict[str, Any]]:
        """Process a single restaurant document and its menu items"""
        documents = []
        restaurant_data = restaurant_doc.to_dict()
        restaurant_id = restaurant_doc.id

        # Get localized field labels
        restaurant_name_label = self.i18n.get_text('restaurant_name', language)
        description_label = self.i18n.get_text('description', language)
        cuisine_types_label = self.i18n.get_text('cuisine_types', language)
        address_label = self.i18n.get_text('address', language)
        services_label = self.i18n.get_text('services', language)
        features_label = self.i18n.get_text('features', language)
        atmosphere_label = self.i18n.get_text('atmosphere', language)
        unknown_restaurant_label = self.i18n.get_text('unknown_restaurant', language)
        not_specified_label = self.i18n.get_text('not_specified', language)
        none_label = self.i18n.get_text('none', language)

        res_name = restaurant_data.get('restaurantName', f'{unknown_restaurant_label} ({restaurant_id})')

        # Restaurant general information
        res_info_parts = [
            f"{restaurant_name_label}: {res_name}",
            f"{description_label}: {restaurant_data.get('description', none_label)}",
            f"{cuisine_types_label}: {', '.join(restaurant_data.get('cuisines', [])) if restaurant_data.get('cuisines') else not_specified_label}",
            f"{address_label}: {restaurant_data.get('address', none_label)}",
            f"{services_label}: {', '.join(restaurant_data.get('services', [])) if restaurant_data.get('services') else not_specified_label}",
            f"{features_label}: {', '.join(restaurant_data.get('features', [])) if restaurant_data.get('features') else not_specified_label}",
            f"{atmosphere_label}: {', '.join(restaurant_data.get('atmosphere', [])) if restaurant_data.get('atmosphere') else not_specified_label}"
        ]

        documents.append({
            "id": f"restaurant_{restaurant_id}",
            "text": ". ".join(filter(None, res_info_parts)),
            "metadata": {
                "type": "restaurant_info",
                "restaurant_id": restaurant_id,
                "name": res_name,
                "language": language
            }
        })

        # Process menu items
        try:
            menu_ref = restaurant_doc.reference.collection('menu')
            menu_docs = menu_ref.stream()

            menu_item_label = self.i18n.get_text('menu_item', language)
            category_label = self.i18n.get_text('category', language)
            price_label = self.i18n.get_text('price', language)
            ingredients_label = self.i18n.get_text('ingredients', language)
            allergens_label = self.i18n.get_text('allergens', language)
            unknown_product_label = self.i18n.get_text('unknown_product', language)

            for menu_doc in menu_docs:
                item_data = menu_doc.to_dict()
                menu_id = menu_doc.id
                item_name = item_data.get('name', f'{unknown_product_label} ({menu_id})')

                menu_item_parts = [
                    f"{menu_item_label}: {item_name} ({restaurant_name_label}: {res_name})",
                    f"{description_label}: {item_data.get('description', none_label)}",
                    f"{category_label}: {item_data.get('category', none_label)}",
                    f"{price_label}: {item_data.get('price', not_specified_label)}",
                    f"{ingredients_label}: {', '.join(item_data.get('ingredients', [])) if item_data.get('ingredients') else not_specified_label}",
                    f"{allergens_label}: {', '.join(item_data.get('allergens', [])) if item_data.get('allergens') else not_specified_label}"
                ]

                documents.append({
                    "id": f"menu_{restaurant_id}_{menu_id}",
                    "text": ". ".join(filter(None, menu_item_parts)),
                    "metadata": {
                        "type": "menu_item",
                        "restaurant_id": restaurant_id,
                        "menu_id": menu_id,
                        "item_name": item_name,
                        "restaurant_name": res_name,
                        "language": language
                    }
                })

        except Exception as e:
            logger.warning(f"Error processing menu items for restaurant {restaurant_id}: {e}")

        return documents

    def load_data(self, language: str = 'en', use_cache: bool = True,
                  use_parallel: bool = True) -> List[Dict[str, Any]]:
        """Load and preprocess data from Firestore"""
        if not self.is_initialized():
            error_msg = self.i18n.get_text('firestore_client_not_initialized', language)
            logger.error(error_msg)
            return []

        # Check cache first
        if use_cache and self._is_cache_valid():
            logger.info(f"Using cached data with {len(self.data_cache)} documents")
            return self.data_cache

        logger.info(self.i18n.get_text('firestore_loading', language))

        try:
            restaurants_ref = self.db.collection('restaurants')
            restaurants_docs = list(restaurants_ref.stream())

            if not restaurants_docs:
                logger.warning("No restaurant documents found in Firestore")
                return []

            documents = []

            if use_parallel and len(restaurants_docs) > 5:
                # Use parallel processing for large datasets
                with ThreadPoolExecutor(max_workers=4) as executor:
                    future_to_restaurant = {
                        executor.submit(self._process_restaurant_data, doc, language): doc
                        for doc in restaurants_docs
                    }

                    for future in as_completed(future_to_restaurant):
                        try:
                            restaurant_docs = future.result()
                            documents.extend(restaurant_docs)
                        except Exception as e:
                            restaurant_doc = future_to_restaurant[future]
                            logger.error(f"Error processing restaurant {restaurant_doc.id}: {e}")
            else:
                # Sequential processing for smaller datasets
                for restaurant_doc in restaurants_docs:
                    try:
                        restaurant_docs = self._process_restaurant_data(restaurant_doc, language)
                        documents.extend(restaurant_docs)
                    except Exception as e:
                        logger.error(f"Error processing restaurant {restaurant_doc.id}: {e}")

            # Update cache
            self.data_cache = documents
            self.cache_timestamp = datetime.now()

            success_msg = self.i18n.get_text('firestore_loaded', language, count=len(documents))
            logger.info(success_msg)
            return documents

        except Exception as e:
            error_msg = self.i18n.get_text('firestore_load_error', language, error=str(e))
            logger.error(error_msg, exc_info=True)
            return []

    def reload_data(self, language: str = 'en') -> bool:
        """Force reload data from Firestore"""
        logger.info(self.i18n.get_text('firestore_reload_request', language))

        # Clear cache
        self.clear_cache()

        # Load fresh data
        documents = self.load_data(language, use_cache=False)

        if documents:
            success_msg = self.i18n.get_text('firestore_reload_success', language, count=len(documents))
            logger.info(success_msg)
            return True
        else:
            error_msg = self.i18n.get_text('firestore_reload_error', language)
            logger.error(error_msg)
            return False

    def clear_cache(self):
        """Clear the data cache"""
        self.data_cache = []
        self.cache_timestamp = None
        logger.info("Firestore data cache cleared")

    def get_cache_info(self) -> Dict[str, Any]:
        """Get information about the current cache"""
        return {
            "cache_size": len(self.data_cache),
            "cache_timestamp": self.cache_timestamp.isoformat() if self.cache_timestamp else None,
            "cache_valid": self._is_cache_valid(),
            "cache_ttl_minutes": self.cache_ttl_minutes
        }

    def get_restaurant_by_id(self, restaurant_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific restaurant by ID"""
        if not self.is_initialized():
            return None

        try:
            doc_ref = self.db.collection('restaurants').document(restaurant_id)
            doc = doc_ref.get()

            if doc.exists:
                return doc.to_dict()
            else:
                logger.warning(f"Restaurant with ID {restaurant_id} not found")
                return None

        except Exception as e:
            logger.error(f"Error fetching restaurant {restaurant_id}: {e}")
            return None

    def search_restaurants(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search restaurants by name or other fields"""
        if not self.is_initialized():
            return []

        try:
            # Simple text search - in production, you might want to use Firestore's full-text search
            restaurants_ref = self.db.collection('restaurants')
            docs = restaurants_ref.limit(limit).stream()

            results = []
            query_lower = query.lower()

            for doc in docs:
                data = doc.to_dict()
                restaurant_name = data.get('restaurantName', '').lower()
                description = data.get('description', '').lower()

                if query_lower in restaurant_name or query_lower in description:
                    data['id'] = doc.id
                    results.append(data)

            return results

        except Exception as e:
            logger.error(f"Error searching restaurants: {e}")
            return []
